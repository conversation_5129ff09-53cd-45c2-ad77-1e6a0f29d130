* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    overflow-x: hidden;
}

.game-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 20px;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    background: linear-gradient(45deg, #ff6b6b, #ffd93d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.controls-info {
    display: flex;
    gap: 40px;
    justify-content: center;
    flex-wrap: wrap;
}

.player-controls {
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.player-controls h3 {
    margin-bottom: 10px;
    color: #ffd93d;
}

/* Canvas Container */
.canvas-container {
    position: relative;
    border: 4px solid #ffd93d;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0,0,0,0.4);
    background: #87CEEB;
    transition: transform 0.1s ease;
}

#gameCanvas {
    display: block;
    background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
}

/* Health Bars */
.health-bars {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    z-index: 10;
}

.health-container {
    width: 300px;
}

.player-name {
    font-weight: bold;
    margin-bottom: 5px;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

.health-bar-bg {
    width: 100%;
    height: 25px;
    background: rgba(0,0,0,0.5);
    border-radius: 15px;
    overflow: hidden;
    border: 2px solid #fff;
}

.health-bar {
    height: 100%;
    border-radius: 13px;
    transition: width 0.3s ease;
    background: linear-gradient(90deg, #ff4757, #ff6b6b, #ff8e8e);
}

.player1 .health-bar {
    background: linear-gradient(90deg, #2ed573, #7bed9f, #70a1ff);
}

.player2 .health-bar {
    background: linear-gradient(90deg, #ff4757, #ff6b6b, #ff8e8e);
}

.health-text {
    text-align: center;
    margin-top: 5px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

/* Game Over Screen */
.game-over {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 100;
}

.game-over-content {
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40px;
    border-radius: 20px;
    border: 3px solid #ffd93d;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
}

.game-over-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #ffd93d;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

/* Start Screen */
.start-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
}

.start-content {
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40px;
    border-radius: 20px;
    border: 3px solid #ffd93d;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    max-width: 600px;
}

.start-content h2 {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #ffd93d;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.instructions {
    margin-top: 30px;
    text-align: left;
}

.instruction-row {
    display: flex;
    gap: 40px;
    justify-content: center;
    margin-top: 15px;
}

.instruction-row div {
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

/* Buttons */
.restart-btn, .start-btn {
    padding: 15px 30px;
    font-size: 1.2rem;
    background: linear-gradient(45deg, #ff6b6b, #ffd93d);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

.restart-btn:hover, .start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 20px rgba(0,0,0,0.4);
}

.restart-btn:active, .start-btn:active {
    transform: translateY(0);
}

/* Footer */
.footer {
    margin-top: 20px;
    text-align: center;
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

/* Combat Effects */
.damage-text {
    position: absolute;
    font-size: 24px;
    font-weight: bold;
    color: #ff0000;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    pointer-events: none;
    animation: damageFloat 1s ease-out forwards;
    z-index: 50;
}

@keyframes damageFloat {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-50px) scale(1.2);
    }
}

.combo-text {
    position: absolute;
    font-size: 20px;
    font-weight: bold;
    color: #ffd93d;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    pointer-events: none;
    animation: comboFloat 1.5s ease-out forwards;
    z-index: 50;
}

@keyframes comboFloat {
    0% {
        opacity: 1;
        transform: translateY(0) scale(0.8);
    }
    50% {
        transform: translateY(-20px) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translateY(-40px) scale(1);
    }
}

/* Blood splatter effect */
.blood-splatter {
    position: absolute;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, #ff0000 0%, #990000 70%, transparent 100%);
    border-radius: 50%;
    pointer-events: none;
    animation: bloodSplatter 2s ease-out forwards;
    z-index: 30;
}

@keyframes bloodSplatter {
    0% {
        opacity: 1;
        transform: scale(0.5);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        opacity: 0;
        transform: scale(1.5);
    }
}

/* Enhanced health bar animations */
.health-bar {
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.health-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: healthShine 2s infinite;
}

@keyframes healthShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive */
@media (max-width: 768px) {
    .header h1 {
        font-size: 2rem;
    }

    .controls-info {
        gap: 20px;
    }

    .player-controls {
        padding: 10px;
    }

    #gameCanvas {
        width: 100%;
        height: auto;
    }

    .health-bars {
        flex-direction: column;
        gap: 10px;
    }

    .health-container {
        width: 100%;
    }

    .instruction-row {
        flex-direction: column;
        gap: 20px;
    }

    .damage-text, .combo-text {
        font-size: 18px;
    }
}
