// Particle system for blood effects
class Particle {
    constructor(x, y, color = '#ff0000') {
        this.x = x;
        this.y = y;
        this.vx = (Math.random() - 0.5) * 10;
        this.vy = (Math.random() - 0.5) * 10 - 2;
        this.gravity = 0.3;
        this.life = 1.0;
        this.decay = Math.random() * 0.02 + 0.01;
        this.size = Math.random() * 4 + 2;
        this.color = color;
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.vy += this.gravity;
        this.life -= this.decay;
        this.vx *= 0.98; // Air resistance
    }

    draw(ctx) {
        if (this.life <= 0) return;

        ctx.save();
        ctx.globalAlpha = this.life;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }

    isDead() {
        return this.life <= 0;
    }
}

class ParticleSystem {
    constructor() {
        this.particles = [];
    }

    addBloodSplash(x, y, count = 25) {
        for (let i = 0; i < count; i++) {
            const colors = ['#ff0000', '#cc0000', '#990000', '#ff3333', '#800000'];
            const color = colors[Math.floor(Math.random() * colors.length)];
            const particle = new Particle(x, y, color);

            // More dramatic blood spray
            particle.vx = (Math.random() - 0.5) * 15;
            particle.vy = (Math.random() - 0.5) * 15 - 3;
            particle.size = Math.random() * 6 + 3;
            particle.gravity = 0.4;
            particle.decay = Math.random() * 0.015 + 0.005;

            this.particles.push(particle);
        }

        // Add larger blood drops
        for (let i = 0; i < 5; i++) {
            const particle = new Particle(x, y, '#990000');
            particle.vx = (Math.random() - 0.5) * 8;
            particle.vy = (Math.random() - 0.5) * 8 - 1;
            particle.size = Math.random() * 8 + 5;
            particle.gravity = 0.5;
            particle.decay = 0.008;
            this.particles.push(particle);
        }
    }

    addHitSpark(x, y, count = 8) {
        for (let i = 0; i < count; i++) {
            const colors = ['#ffff00', '#ffd700', '#ffaa00', '#ffffff'];
            const color = colors[Math.floor(Math.random() * colors.length)];
            const particle = new Particle(x, y, color);
            particle.vx = (Math.random() - 0.5) * 15;
            particle.vy = (Math.random() - 0.5) * 15;
            particle.gravity = 0.1;
            particle.size = Math.random() * 3 + 1;
            this.particles.push(particle);
        }
    }

    update() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            this.particles[i].update();
            if (this.particles[i].isDead()) {
                this.particles.splice(i, 1);
            }
        }
    }

    draw(ctx) {
        this.particles.forEach(particle => particle.draw(ctx));
    }

    clear() {
        this.particles = [];
    }
}
