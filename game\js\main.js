// Main game initialization and event handling
document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('gameCanvas');
    const game = new Game(canvas);

    // UI Elements
    const pvpBtn = document.getElementById('pvpBtn');
    const aiBtn = document.getElementById('aiBtn');
    const restartBtn = document.getElementById('restartBtn');
    const startScreen = document.getElementById('startScreen');
    const player2Controls = document.getElementById('player2Controls');

    // Event Listeners
    pvpBtn.addEventListener('click', function() {
        player2Controls.style.display = 'block';
        game.start('pvp');
    });

    aiBtn.addEventListener('click', function() {
        player2Controls.style.display = 'none';
        game.start('ai');
    });

    restartBtn.addEventListener('click', function() {
        // Restart with the same game mode
        game.start(game.gameMode);
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Start game with Enter or Space
        if ((e.code === 'Enter' || e.code === 'Space') && game.gameState === 'start') {
            e.preventDefault();
            game.start();
        }

        // Restart game with R
        if (e.code === 'KeyR' && game.gameState === 'gameOver') {
            e.preventDefault();
            game.start();
        }

        // Pause/Resume with P (optional feature)
        if (e.code === 'KeyP' && game.gameState === 'playing') {
            e.preventDefault();
            // Could implement pause functionality here
        }
    });

    // Prevent default behavior for game keys
    document.addEventListener('keydown', function(e) {
        const gameKeys = [
            'KeyA', 'KeyD', 'KeyW', 'KeyS',
            'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'
        ];

        if (gameKeys.includes(e.code)) {
            e.preventDefault();
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        // Could implement responsive canvas resizing here
        // For now, we'll keep the fixed size for consistent gameplay
    });

    // Handle visibility change (pause when tab is not active)
    document.addEventListener('visibilitychange', function() {
        if (document.hidden && game.gameState === 'playing') {
            // Could pause the game here
            console.log('Game paused - tab not visible');
        } else if (!document.hidden && game.gameState === 'playing') {
            // Resume game
            console.log('Game resumed - tab visible');
        }
    });

    // Touch controls for mobile (basic implementation)
    let touchStartX = 0;
    let touchStartY = 0;

    canvas.addEventListener('touchstart', function(e) {
        e.preventDefault();
        const touch = e.touches[0];
        touchStartX = touch.clientX;
        touchStartY = touch.clientY;
    });

    canvas.addEventListener('touchend', function(e) {
        e.preventDefault();
        const touch = e.changedTouches[0];
        const touchEndX = touch.clientX;
        const touchEndY = touch.clientY;

        const deltaX = touchEndX - touchStartX;
        const deltaY = touchEndY - touchStartY;

        // Simple swipe detection for mobile controls
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            if (deltaX > 50) {
                // Swipe right - could trigger right movement
                console.log('Swipe right detected');
            } else if (deltaX < -50) {
                // Swipe left - could trigger left movement
                console.log('Swipe left detected');
            }
        } else {
            if (deltaY < -50) {
                // Swipe up - could trigger jump
                console.log('Swipe up detected');
            } else if (deltaY > 50) {
                // Swipe down - could trigger attack
                console.log('Swipe down detected');
            }
        }
    });

    // Prevent context menu on right click
    canvas.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });

    // Show initial start screen
    console.log('Game initialized. Click Start to begin!');

    // Optional: Add sound effects
    const sounds = {
        hit: null,
        jump: null,
        attack: null,
        gameOver: null
    };

    // Function to load sounds (if audio files are available)
    function loadSounds() {
        try {
            // Uncomment and add audio files if desired
            // sounds.hit = new Audio('sounds/hit.mp3');
            // sounds.jump = new Audio('sounds/jump.mp3');
            // sounds.attack = new Audio('sounds/attack.mp3');
            // sounds.gameOver = new Audio('sounds/gameOver.mp3');
        } catch (error) {
            console.log('Audio files not found, continuing without sound');
        }
    }

    // Function to play sound
    function playSound(soundName) {
        if (sounds[soundName]) {
            sounds[soundName].currentTime = 0;
            sounds[soundName].play().catch(e => {
                console.log('Could not play sound:', e);
            });
        }
    }

    // Initialize sounds
    loadSounds();

    // Make sounds available globally for game events
    window.gameAudio = {
        play: playSound
    };

    // Performance monitoring (optional)
    let frameCount = 0;
    let lastFPSTime = performance.now();

    function updateFPS() {
        frameCount++;
        const currentTime = performance.now();

        if (currentTime - lastFPSTime >= 1000) {
            const fps = Math.round((frameCount * 1000) / (currentTime - lastFPSTime));
            console.log(`FPS: ${fps}`);
            frameCount = 0;
            lastFPSTime = currentTime;
        }

        requestAnimationFrame(updateFPS);
    }

    // Start FPS monitoring (comment out if not needed)
    // updateFPS();
});
