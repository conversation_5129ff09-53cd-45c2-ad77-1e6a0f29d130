class Game {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.width = canvas.width;
        this.height = canvas.height;
        this.groundY = this.height - 50;
        
        // Game state
        this.gameState = 'start'; // 'start', 'playing', 'gameOver'
        this.winner = null;
        
        // Players
        this.player1 = new Player(100, this.groundY - 80, '#4a90e2', {
            left: 'KeyA',
            right: 'KeyD', 
            jump: 'KeyW',
            attack: 'KeyS'
        });
        
        this.player2 = new Player(this.width - 140, this.groundY - 80, '#e74c3c', {
            left: 'ArrowLeft',
            right: 'ArrowRight',
            jump: 'ArrowUp', 
            attack: 'ArrowDown'
        });
        
        // Input handling
        this.keys = {};
        this.setupInput();
        
        // Particle system
        this.particleSystem = new ParticleSystem();
        
        // UI elements
        this.player1HealthBar = document.getElementById('player1Health');
        this.player2HealthBar = document.getElementById('player2Health');
        this.player1HealthText = document.getElementById('player1HealthText');
        this.player2HealthText = document.getElementById('player2HealthText');
        this.gameOverScreen = document.getElementById('gameOverScreen');
        this.winnerText = document.getElementById('winnerText');
        this.startScreen = document.getElementById('startScreen');
        
        // Game loop
        this.lastTime = 0;
        this.gameLoop = this.gameLoop.bind(this);
    }

    setupInput() {
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
        });

        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
    }

    start() {
        this.gameState = 'playing';
        this.startScreen.style.display = 'none';
        this.gameOverScreen.style.display = 'none';
        this.resetGame();
        requestAnimationFrame(this.gameLoop);
    }

    resetGame() {
        this.player1.reset(100, this.groundY - 80);
        this.player2.reset(this.width - 140, this.groundY - 80);
        this.particleSystem.clear();
        this.winner = null;
        this.updateHealthBars();
    }

    gameLoop(currentTime) {
        if (this.gameState !== 'playing') return;

        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;

        this.update();
        this.draw();

        requestAnimationFrame(this.gameLoop);
    }

    update() {
        // Update players
        this.player1.update(this.keys, this.groundY, this.width);
        this.player2.update(this.keys, this.groundY, this.width);

        // Check combat
        this.checkCombat();

        // Update particles
        this.particleSystem.update();

        // Update UI
        this.updateHealthBars();

        // Check win condition
        this.checkWinCondition();
    }

    checkCombat() {
        // Player 1 attacking Player 2
        const p1AttackBox = this.player1.getAttackBox();
        if (p1AttackBox && this.isColliding(p1AttackBox, this.player2)) {
            this.player2.takeDamage(this.player1.damage, this.player1.x);
            this.particleSystem.addBloodSplash(
                this.player2.x + this.player2.width/2, 
                this.player2.y + this.player2.height/2
            );
            this.particleSystem.addHitSpark(
                this.player2.x + this.player2.width/2, 
                this.player2.y + this.player2.height/2
            );
            this.player1.attackDuration = 0; // Prevent multiple hits
        }

        // Player 2 attacking Player 1
        const p2AttackBox = this.player2.getAttackBox();
        if (p2AttackBox && this.isColliding(p2AttackBox, this.player1)) {
            this.player1.takeDamage(this.player2.damage, this.player2.x);
            this.particleSystem.addBloodSplash(
                this.player1.x + this.player1.width/2, 
                this.player1.y + this.player1.height/2
            );
            this.particleSystem.addHitSpark(
                this.player1.x + this.player1.width/2, 
                this.player1.y + this.player1.height/2
            );
            this.player2.attackDuration = 0; // Prevent multiple hits
        }
    }

    isColliding(rect1, rect2) {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    }

    updateHealthBars() {
        // Player 1
        const p1HealthPercent = (this.player1.health / this.player1.maxHealth) * 100;
        this.player1HealthBar.style.width = p1HealthPercent + '%';
        this.player1HealthText.textContent = `${this.player1.health}/${this.player1.maxHealth}`;

        // Player 2
        const p2HealthPercent = (this.player2.health / this.player2.maxHealth) * 100;
        this.player2HealthBar.style.width = p2HealthPercent + '%';
        this.player2HealthText.textContent = `${this.player2.health}/${this.player2.maxHealth}`;
    }

    checkWinCondition() {
        if (this.player1.isDead()) {
            this.endGame('Người chơi 2');
        } else if (this.player2.isDead()) {
            this.endGame('Người chơi 1');
        }
    }

    endGame(winner) {
        this.gameState = 'gameOver';
        this.winner = winner;
        this.winnerText.textContent = `${winner} Thắng!`;
        this.gameOverScreen.style.display = 'flex';
    }

    draw() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.width, this.height);

        // Draw background
        this.drawBackground();

        // Draw ground
        this.drawGround();

        // Draw players
        this.player1.draw(this.ctx);
        this.player2.draw(this.ctx);

        // Draw attack boxes (for debugging)
        // this.drawAttackBoxes();

        // Draw particles
        this.particleSystem.draw(this.ctx);
    }

    drawBackground() {
        // Sky gradient
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.height);
        gradient.addColorStop(0, '#87CEEB');
        gradient.addColorStop(1, '#98FB98');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.width, this.height);

        // Clouds
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        this.drawCloud(200, 100, 60);
        this.drawCloud(500, 80, 80);
        this.drawCloud(800, 120, 70);
        this.drawCloud(1000, 90, 50);
    }

    drawCloud(x, y, size) {
        this.ctx.beginPath();
        this.ctx.arc(x, y, size * 0.5, 0, Math.PI * 2);
        this.ctx.arc(x + size * 0.3, y, size * 0.7, 0, Math.PI * 2);
        this.ctx.arc(x + size * 0.6, y, size * 0.5, 0, Math.PI * 2);
        this.ctx.arc(x + size * 0.2, y - size * 0.3, size * 0.4, 0, Math.PI * 2);
        this.ctx.arc(x + size * 0.4, y - size * 0.3, size * 0.6, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawGround() {
        // Grass
        this.ctx.fillStyle = '#228B22';
        this.ctx.fillRect(0, this.groundY, this.width, this.height - this.groundY);

        // Dirt
        this.ctx.fillStyle = '#8B4513';
        this.ctx.fillRect(0, this.groundY + 30, this.width, this.height - this.groundY - 30);
    }

    drawAttackBoxes() {
        // Debug: Draw attack boxes
        this.ctx.strokeStyle = 'rgba(255, 0, 0, 0.5)';
        this.ctx.lineWidth = 2;

        const p1AttackBox = this.player1.getAttackBox();
        if (p1AttackBox) {
            this.ctx.strokeRect(p1AttackBox.x, p1AttackBox.y, p1AttackBox.width, p1AttackBox.height);
        }

        const p2AttackBox = this.player2.getAttackBox();
        if (p2AttackBox) {
            this.ctx.strokeRect(p2AttackBox.x, p2AttackBox.y, p2AttackBox.width, p2AttackBox.height);
        }
    }
}
