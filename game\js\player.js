class Player {
    constructor(x, y, color, controls) {
        this.x = x;
        this.y = y;
        this.width = 40;
        this.height = 80;
        this.color = color;
        this.controls = controls;

        // Physics
        this.vx = 0;
        this.vy = 0;
        this.speed = 5;
        this.jumpPower = 15;
        this.gravity = 0.8;
        this.onGround = false;

        // Combat
        this.health = 100;
        this.maxHealth = 100;
        this.damage = 20;
        this.attackRange = 70;
        this.attackCooldown = 0;
        this.attackDuration = 0;
        this.isAttacking = false;
        this.hasHit = false;
        this.comboCount = 0;
        this.lastAttackTime = 0;

        // Animation
        this.facingRight = true;
        this.animationFrame = 0;
        this.animationSpeed = 0.2;
        this.walkCycle = 0;
        this.attackAnimFrame = 0;
        this.swordAngle = 0;
        this.swordSwingSpeed = 0;

        // Hit effects
        this.hitFlash = 0;
        this.knockback = 0;
        this.bloodTrail = [];
        this.isMoving = false;

        // Visual effects
        this.shadowOffset = 0;
        this.breathingOffset = 0;
    }

    update(keys, groundY, canvasWidth) {
        this.handleInput(keys);
        this.updatePhysics(groundY, canvasWidth);
        this.updateCombat();
        this.updateAnimation();
        this.updateEffects();
    }

    handleInput(keys) {
        // Check if moving
        this.isMoving = false;

        // Movement
        if (keys[this.controls.left] && !this.isAttacking) {
            this.vx = -this.speed;
            this.facingRight = false;
            this.isMoving = true;
        } else if (keys[this.controls.right] && !this.isAttacking) {
            this.vx = this.speed;
            this.facingRight = true;
            this.isMoving = true;
        } else {
            this.vx *= 0.8; // Friction
        }

        // Jump
        if (keys[this.controls.jump] && this.onGround && !this.isAttacking) {
            this.vy = -this.jumpPower;
            this.onGround = false;
        }

        // Attack
        if (keys[this.controls.attack] && this.attackCooldown <= 0 && !this.isAttacking) {
            this.startAttack();
        }
    }

    updatePhysics(groundY, canvasWidth) {
        // Apply knockback
        this.x += this.knockback;
        this.knockback *= 0.9;

        // Horizontal movement
        this.x += this.vx;

        // Boundary check
        if (this.x < 0) this.x = 0;
        if (this.x + this.width > canvasWidth) this.x = canvasWidth - this.width;

        // Vertical movement
        this.y += this.vy;
        this.vy += this.gravity;

        // Ground collision
        if (this.y + this.height >= groundY) {
            this.y = groundY - this.height;
            this.vy = 0;
            this.onGround = true;
        }
    }

    updateCombat() {
        if (this.attackCooldown > 0) {
            this.attackCooldown--;
        }

        if (this.attackDuration > 0) {
            this.attackDuration--;
            if (this.attackDuration <= 0) {
                this.isAttacking = false;
                this.hasHit = false; // Reset hit flag when attack ends
            }
        }
    }

    updateAnimation() {
        // Walking animation
        if (this.isMoving && this.onGround) {
            this.walkCycle += 0.3;
            if (this.walkCycle >= Math.PI * 2) {
                this.walkCycle = 0;
            }
        }

        // Attack animation
        if (this.isAttacking) {
            this.attackAnimFrame += 0.4;
            this.swordSwingSpeed = Math.sin(this.attackAnimFrame * 2) * 15;
            this.swordAngle = Math.sin(this.attackAnimFrame) * 90;
        } else {
            this.attackAnimFrame = 0;
            this.swordAngle = 0;
            this.swordSwingSpeed = 0;
        }

        // Breathing animation
        this.breathingOffset = Math.sin(Date.now() * 0.003) * 1;

        // General animation frame
        this.animationFrame += this.animationSpeed;
        if (this.animationFrame >= 4) {
            this.animationFrame = 0;
        }
    }

    updateEffects() {
        if (this.hitFlash > 0) {
            this.hitFlash--;
        }
    }

    startAttack() {
        this.isAttacking = true;
        this.attackDuration = 20;
        this.attackCooldown = 35;
        this.attackAnimFrame = 0;
        this.hasHit = false; // Reset hit flag for new attack

        // Combo system
        const currentTime = Date.now();
        if (currentTime - this.lastAttackTime < 1000) {
            this.comboCount++;
        } else {
            this.comboCount = 1;
        }
        this.lastAttackTime = currentTime;

        // Increase damage for combos
        if (this.comboCount > 1) {
            this.damage = 20 + (this.comboCount * 5);
        } else {
            this.damage = 20;
        }
    }

    getAttackBox() {
        if (!this.isAttacking) return null;

        const attackX = this.facingRight ?
            this.x + this.width :
            this.x - this.attackRange;

        return {
            x: attackX,
            y: this.y,
            width: this.attackRange,
            height: this.height
        };
    }

    takeDamage(damage, attackerX) {
        this.health -= damage;
        this.hitFlash = 15;

        // Knockback
        const knockbackDirection = this.x < attackerX ? -1 : 1;
        this.knockback = knockbackDirection * 12;

        // Add blood trail effect
        for (let i = 0; i < 5; i++) {
            this.bloodTrail.push({
                x: this.x + this.width/2 + (Math.random() - 0.5) * 20,
                y: this.y + this.height/2 + (Math.random() - 0.5) * 20,
                life: 30,
                size: Math.random() * 3 + 2
            });
        }

        if (this.health < 0) {
            this.health = 0;
        }
    }

    draw(ctx) {
        ctx.save();

        // Draw shadow
        this.drawShadow(ctx);

        // Draw blood trail
        this.drawBloodTrail(ctx);

        // Hit flash effect
        if (this.hitFlash > 0) {
            ctx.globalAlpha = 0.7;
            ctx.shadowColor = '#ff0000';
            ctx.shadowBlur = 10;
            ctx.fillStyle = this.hitFlash % 4 < 2 ? '#ffffff' : '#ff6666';
        } else {
            ctx.fillStyle = this.color;
            ctx.shadowColor = 'rgba(0,0,0,0.3)';
            ctx.shadowBlur = 3;
            ctx.shadowOffsetY = 2;
        }

        // Draw body
        this.drawBody(ctx);

        // Draw sword
        this.drawSword(ctx);

        // Draw attack effect
        if (this.isAttacking) {
            this.drawAttackEffect(ctx);
        }

        ctx.restore();
    }

    drawBody(ctx) {
        const breathingY = this.y + this.breathingOffset;

        // Main body with breathing animation
        ctx.fillRect(this.x, breathingY + 20, this.width, this.height - 40);

        // Head with breathing
        ctx.beginPath();
        ctx.arc(this.x + this.width/2, breathingY + 15, 15, 0, Math.PI * 2);
        ctx.fill();

        // Arms with walking animation
        let armOffset = 0;
        if (this.isMoving) {
            armOffset = Math.sin(this.walkCycle) * 8;
        }

        // Left arm
        ctx.fillRect(this.x - 5, breathingY + 25 + armOffset, 10, 30);
        // Right arm
        ctx.fillRect(this.x + this.width - 5, breathingY + 25 - armOffset, 10, 30);

        // Legs with walking animation
        let legOffset1 = 0, legOffset2 = 0;
        if (this.isMoving) {
            legOffset1 = Math.sin(this.walkCycle) * 5;
            legOffset2 = Math.sin(this.walkCycle + Math.PI) * 5;
        }

        // Left leg
        ctx.fillRect(this.x + 5, breathingY + this.height - 25 + legOffset1, 12, 25);
        // Right leg
        ctx.fillRect(this.x + this.width - 17, breathingY + this.height - 25 + legOffset2, 12, 25);

        // Eyes
        ctx.fillStyle = '#000000';
        const eyeY = breathingY + 10;
        if (this.facingRight) {
            ctx.fillRect(this.x + this.width/2 + 3, eyeY, 3, 3);
            ctx.fillRect(this.x + this.width/2 + 8, eyeY, 3, 3);
        } else {
            ctx.fillRect(this.x + this.width/2 - 11, eyeY, 3, 3);
            ctx.fillRect(this.x + this.width/2 - 6, eyeY, 3, 3);
        }

        // Mouth (expression based on health)
        ctx.fillStyle = '#000000';
        const mouthY = breathingY + 18;
        if (this.health < 30) {
            // Injured expression
            ctx.fillRect(this.x + this.width/2 - 3, mouthY, 6, 2);
        } else {
            // Normal expression
            ctx.fillRect(this.x + this.width/2 - 2, mouthY, 4, 1);
        }
    }

    drawSword(ctx) {
        ctx.save();

        const swordLength = 55;
        const swordWidth = 8;
        const handleLength = 18;

        // Sword position
        const swordX = this.facingRight ? this.x + this.width - 10 : this.x + 10;
        const swordY = this.y + 35 + this.breathingOffset;

        // Apply sword rotation for attack animation
        if (this.isAttacking) {
            ctx.translate(swordX, swordY);
            ctx.rotate((this.swordAngle * Math.PI / 180) * (this.facingRight ? 1 : -1));
            ctx.translate(-swordX, -swordY);
        }

        if (this.facingRight) {
            // Sword blade with gradient
            const gradient = ctx.createLinearGradient(swordX, swordY, swordX + swordLength, swordY);
            gradient.addColorStop(0, '#e0e0e0');
            gradient.addColorStop(0.5, '#ffffff');
            gradient.addColorStop(1, '#c0c0c0');
            ctx.fillStyle = gradient;
            ctx.fillRect(swordX, swordY, swordLength, swordWidth);

            // Sword edge highlight
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(swordX, swordY + 1, swordLength, 2);

            // Sword handle
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(swordX - handleLength, swordY - 2, handleLength, swordWidth + 4);

            // Handle grip
            ctx.fillStyle = '#654321';
            for (let i = 0; i < 3; i++) {
                ctx.fillRect(swordX - handleLength + 3 + i * 4, swordY, 2, swordWidth + 4);
            }

            // Guard
            ctx.fillStyle = '#FFD700';
            ctx.fillRect(swordX - 3, swordY - 5, 6, swordWidth + 10);
        } else {
            // Sword blade with gradient
            const gradient = ctx.createLinearGradient(swordX - swordLength, swordY, swordX, swordY);
            gradient.addColorStop(0, '#c0c0c0');
            gradient.addColorStop(0.5, '#ffffff');
            gradient.addColorStop(1, '#e0e0e0');
            ctx.fillStyle = gradient;
            ctx.fillRect(swordX - swordLength, swordY, swordLength, swordWidth);

            // Sword edge highlight
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(swordX - swordLength, swordY + 1, swordLength, 2);

            // Sword handle
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(swordX, swordY - 2, handleLength, swordWidth + 4);

            // Handle grip
            ctx.fillStyle = '#654321';
            for (let i = 0; i < 3; i++) {
                ctx.fillRect(swordX + 3 + i * 4, swordY, 2, swordWidth + 4);
            }

            // Guard
            ctx.fillStyle = '#FFD700';
            ctx.fillRect(swordX - 3, swordY - 5, 6, swordWidth + 10);
        }

        ctx.restore();
    }

    isDead() {
        return this.health <= 0;
    }

    reset(x, y) {
        this.x = x;
        this.y = y;
        this.vx = 0;
        this.vy = 0;
        this.health = this.maxHealth;
        this.attackCooldown = 0;
        this.attackDuration = 0;
        this.isAttacking = false;
        this.hasHit = false;
        this.hitFlash = 0;
        this.knockback = 0;
        this.onGround = false;
        this.bloodTrail = [];
        this.comboCount = 0;
        this.walkCycle = 0;
        this.attackAnimFrame = 0;
        this.swordAngle = 0;
    }

    drawShadow(ctx) {
        ctx.save();
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.beginPath();
        ctx.ellipse(this.x + this.width/2, this.y + this.height + 5, this.width/2, 8, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }

    drawBloodTrail(ctx) {
        ctx.save();
        for (let i = this.bloodTrail.length - 1; i >= 0; i--) {
            const blood = this.bloodTrail[i];
            blood.life--;
            blood.y += 2; // Gravity effect

            if (blood.life <= 0) {
                this.bloodTrail.splice(i, 1);
                continue;
            }

            ctx.globalAlpha = blood.life / 30;
            ctx.fillStyle = '#cc0000';
            ctx.beginPath();
            ctx.arc(blood.x, blood.y, blood.size, 0, Math.PI * 2);
            ctx.fill();
        }
        ctx.restore();
    }

    drawAttackEffect(ctx) {
        if (!this.isAttacking) return;

        ctx.save();

        // Attack slash effect
        const slashLength = 80;
        const slashWidth = 15;
        const centerX = this.facingRight ? this.x + this.width + 20 : this.x - 20;
        const centerY = this.y + this.height/2;

        // Create slash trail
        ctx.globalAlpha = 0.6;
        const gradient = ctx.createLinearGradient(
            centerX - slashLength/2, centerY,
            centerX + slashLength/2, centerY
        );
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0)');
        gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.8)');
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

        ctx.fillStyle = gradient;

        // Draw slash arc
        ctx.translate(centerX, centerY);
        ctx.rotate(this.swordAngle * Math.PI / 180);
        ctx.fillRect(-slashLength/2, -slashWidth/2, slashLength, slashWidth);

        // Add sparkle effects
        for (let i = 0; i < 5; i++) {
            const sparkX = (Math.random() - 0.5) * slashLength;
            const sparkY = (Math.random() - 0.5) * slashWidth;
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(sparkX, sparkY, 3, 3);
        }

        ctx.restore();
    }
}
