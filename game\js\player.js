class Player {
    constructor(x, y, color, controls) {
        this.x = x;
        this.y = y;
        this.width = 40;
        this.height = 80;
        this.color = color;
        this.controls = controls;
        
        // Physics
        this.vx = 0;
        this.vy = 0;
        this.speed = 5;
        this.jumpPower = 15;
        this.gravity = 0.8;
        this.onGround = false;
        
        // Combat
        this.health = 100;
        this.maxHealth = 100;
        this.damage = 20;
        this.attackRange = 60;
        this.attackCooldown = 0;
        this.attackDuration = 0;
        this.isAttacking = false;
        
        // Animation
        this.facingRight = true;
        this.animationFrame = 0;
        this.animationSpeed = 0.2;
        
        // Hit effects
        this.hitFlash = 0;
        this.knockback = 0;
    }

    update(keys, groundY, canvasWidth) {
        this.handleInput(keys);
        this.updatePhysics(groundY, canvasWidth);
        this.updateCombat();
        this.updateAnimation();
        this.updateEffects();
    }

    handleInput(keys) {
        // Movement
        if (keys[this.controls.left]) {
            this.vx = -this.speed;
            this.facingRight = false;
        } else if (keys[this.controls.right]) {
            this.vx = this.speed;
            this.facingRight = true;
        } else {
            this.vx *= 0.8; // Friction
        }

        // Jump
        if (keys[this.controls.jump] && this.onGround) {
            this.vy = -this.jumpPower;
            this.onGround = false;
        }

        // Attack
        if (keys[this.controls.attack] && this.attackCooldown <= 0 && !this.isAttacking) {
            this.startAttack();
        }
    }

    updatePhysics(groundY, canvasWidth) {
        // Apply knockback
        this.x += this.knockback;
        this.knockback *= 0.9;

        // Horizontal movement
        this.x += this.vx;
        
        // Boundary check
        if (this.x < 0) this.x = 0;
        if (this.x + this.width > canvasWidth) this.x = canvasWidth - this.width;

        // Vertical movement
        this.y += this.vy;
        this.vy += this.gravity;

        // Ground collision
        if (this.y + this.height >= groundY) {
            this.y = groundY - this.height;
            this.vy = 0;
            this.onGround = true;
        }
    }

    updateCombat() {
        if (this.attackCooldown > 0) {
            this.attackCooldown--;
        }

        if (this.attackDuration > 0) {
            this.attackDuration--;
            if (this.attackDuration <= 0) {
                this.isAttacking = false;
            }
        }
    }

    updateAnimation() {
        this.animationFrame += this.animationSpeed;
        if (this.animationFrame >= 4) {
            this.animationFrame = 0;
        }
    }

    updateEffects() {
        if (this.hitFlash > 0) {
            this.hitFlash--;
        }
    }

    startAttack() {
        this.isAttacking = true;
        this.attackDuration = 15;
        this.attackCooldown = 30;
    }

    getAttackBox() {
        if (!this.isAttacking) return null;

        const attackX = this.facingRight ? 
            this.x + this.width : 
            this.x - this.attackRange;

        return {
            x: attackX,
            y: this.y,
            width: this.attackRange,
            height: this.height
        };
    }

    takeDamage(damage, attackerX) {
        this.health -= damage;
        this.hitFlash = 10;
        
        // Knockback
        const knockbackDirection = this.x < attackerX ? -1 : 1;
        this.knockback = knockbackDirection * 8;

        if (this.health < 0) {
            this.health = 0;
        }
    }

    draw(ctx) {
        ctx.save();

        // Hit flash effect
        if (this.hitFlash > 0) {
            ctx.globalAlpha = 0.5;
            ctx.fillStyle = '#ffffff';
        } else {
            ctx.fillStyle = this.color;
        }

        // Draw body
        this.drawBody(ctx);

        // Draw sword if attacking
        if (this.isAttacking) {
            this.drawSword(ctx);
        }

        ctx.restore();
    }

    drawBody(ctx) {
        // Main body
        ctx.fillRect(this.x, this.y + 20, this.width, this.height - 40);
        
        // Head
        ctx.beginPath();
        ctx.arc(this.x + this.width/2, this.y + 15, 15, 0, Math.PI * 2);
        ctx.fill();

        // Arms
        const armOffset = Math.sin(this.animationFrame) * 5;
        ctx.fillRect(this.x - 5, this.y + 25 + armOffset, 10, 30);
        ctx.fillRect(this.x + this.width - 5, this.y + 25 - armOffset, 10, 30);

        // Legs
        const legOffset = Math.sin(this.animationFrame + Math.PI) * 3;
        ctx.fillRect(this.x + 5, this.y + this.height - 25, 12, 25);
        ctx.fillRect(this.x + this.width - 17, this.y + this.height - 25 + legOffset, 12, 25);

        // Eyes
        ctx.fillStyle = '#000000';
        const eyeY = this.y + 10;
        if (this.facingRight) {
            ctx.fillRect(this.x + this.width/2 + 3, eyeY, 3, 3);
            ctx.fillRect(this.x + this.width/2 + 8, eyeY, 3, 3);
        } else {
            ctx.fillRect(this.x + this.width/2 - 11, eyeY, 3, 3);
            ctx.fillRect(this.x + this.width/2 - 6, eyeY, 3, 3);
        }
    }

    drawSword(ctx) {
        ctx.fillStyle = '#c0c0c0';
        
        const swordLength = 50;
        const swordWidth = 6;
        
        if (this.facingRight) {
            // Sword blade
            ctx.fillRect(this.x + this.width, this.y + 30, swordLength, swordWidth);
            // Sword handle
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(this.x + this.width - 15, this.y + 28, 15, 10);
        } else {
            // Sword blade
            ctx.fillRect(this.x - swordLength, this.y + 30, swordLength, swordWidth);
            // Sword handle
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(this.x, this.y + 28, 15, 10);
        }
    }

    isDead() {
        return this.health <= 0;
    }

    reset(x, y) {
        this.x = x;
        this.y = y;
        this.vx = 0;
        this.vy = 0;
        this.health = this.maxHealth;
        this.attackCooldown = 0;
        this.attackDuration = 0;
        this.isAttacking = false;
        this.hitFlash = 0;
        this.knockback = 0;
        this.onGround = false;
    }
}
