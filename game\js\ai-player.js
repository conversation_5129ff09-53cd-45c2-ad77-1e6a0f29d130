class AIPlayer extends Player {
    constructor(x, y, color, controls, difficulty = 'hard') {
        super(x, y, color, controls);
        
        // AI specific properties
        this.difficulty = difficulty;
        this.target = null;
        this.aiState = 'idle'; // 'idle', 'approach', 'attack', 'defend', 'retreat'
        this.reactionTime = 0;
        this.nextAction = 0;
        this.lastPlayerDistance = 0;
        this.consecutiveAttacks = 0;
        this.lastAttackTime = 0;
        
        // AI behavior parameters (very hard difficulty)
        this.aggressiveness = 0.9; // 0-1, higher = more aggressive
        this.reactionSpeed = 2; // frames to react (very fast)
        this.attackAccuracy = 0.95; // 0-1, higher = more accurate
        this.defenseSkill = 0.9; // 0-1, higher = better defense
        this.comboChance = 0.8; // chance to continue combo
        this.perfectTiming = 0.85; // chance for perfect timing
        
        // Advanced AI features
        this.playerPattern = []; // Track player's movement patterns
        this.predictedPlayerPos = { x: 0, y: 0 };
        this.feintChance = 0.3; // chance to feint attack
        this.counterAttackWindow = 0;
        this.isFeinting = false;
        this.adaptiveTimer = 0;
    }

    update(keys, groundY, canvasWidth, player) {
        this.target = player;
        
        // Update AI decision making
        this.updateAI();
        
        // Simulate key presses based on AI decisions
        this.simulateInput(keys);
        
        // Call parent update
        super.update(keys, groundY, canvasWidth);
        
        // Update AI specific timers
        this.updateAITimers();
    }

    updateAI() {
        if (!this.target) return;
        
        // Calculate distance and relative position
        const distance = Math.abs(this.x - this.target.x);
        const isPlayerLeft = this.target.x < this.x;
        const isPlayerRight = this.target.x > this.x;
        
        // Track player movement patterns
        this.trackPlayerPattern();
        
        // Predict player position
        this.predictPlayerMovement();
        
        // Adaptive difficulty - get harder over time
        this.adaptDifficulty();
        
        // Decide AI state based on situation
        this.decideAIState(distance, isPlayerLeft, isPlayerRight);
        
        // Update reaction time
        if (this.reactionTime > 0) {
            this.reactionTime--;
        }
    }

    trackPlayerPattern() {
        const playerData = {
            x: this.target.x,
            vx: this.target.vx,
            isAttacking: this.target.isAttacking,
            timestamp: Date.now()
        };
        
        this.playerPattern.push(playerData);
        
        // Keep only recent data (last 60 frames)
        if (this.playerPattern.length > 60) {
            this.playerPattern.shift();
        }
    }

    predictPlayerMovement() {
        if (this.playerPattern.length < 5) return;
        
        const recent = this.playerPattern.slice(-5);
        const avgVx = recent.reduce((sum, data) => sum + data.vx, 0) / recent.length;
        
        // Predict where player will be in next few frames
        this.predictedPlayerPos.x = this.target.x + (avgVx * 10);
        this.predictedPlayerPos.y = this.target.y;
    }

    adaptDifficulty() {
        this.adaptiveTimer++;
        
        // Get progressively harder every 30 seconds
        if (this.adaptiveTimer > 1800) { // 30 seconds at 60fps
            this.aggressiveness = Math.min(0.95, this.aggressiveness + 0.02);
            this.attackAccuracy = Math.min(0.98, this.attackAccuracy + 0.01);
            this.reactionSpeed = Math.max(1, this.reactionSpeed - 0.2);
            this.adaptiveTimer = 0;
        }
    }

    decideAIState(distance, isPlayerLeft, isPlayerRight) {
        // Counter-attack window after player attacks
        if (this.target.isAttacking && this.counterAttackWindow === 0) {
            this.counterAttackWindow = 20;
        }
        
        if (this.counterAttackWindow > 0) {
            this.counterAttackWindow--;
        }
        
        // Perfect counter-attack timing
        if (this.target.attackDuration <= 5 && this.target.attackDuration > 0 && 
            distance < this.attackRange + 20 && Math.random() < this.perfectTiming) {
            this.aiState = 'counter';
            return;
        }
        
        // Health-based behavior
        const healthRatio = this.health / this.maxHealth;
        const playerHealthRatio = this.target.health / this.target.maxHealth;
        
        if (healthRatio < 0.3 && playerHealthRatio > 0.7) {
            // Desperate mode - ultra aggressive
            this.aiState = 'desperate';
            this.aggressiveness = 1.0;
        } else if (distance < this.attackRange + 10) {
            // Close combat
            if (this.target.isAttacking && Math.random() < this.defenseSkill) {
                this.aiState = 'defend';
            } else if (this.attackCooldown === 0 && Math.random() < this.aggressiveness) {
                this.aiState = 'attack';
            } else {
                this.aiState = 'close_combat';
            }
        } else if (distance < 150) {
            // Medium range - approach or retreat
            if (healthRatio > playerHealthRatio || Math.random() < this.aggressiveness) {
                this.aiState = 'approach';
            } else {
                this.aiState = 'tactical';
            }
        } else {
            // Long range - approach
            this.aiState = 'approach';
        }
    }

    simulateInput(keys) {
        // Reset all keys
        keys[this.controls.left] = false;
        keys[this.controls.right] = false;
        keys[this.controls.jump] = false;
        keys[this.controls.attack] = false;
        
        // Skip if in reaction time
        if (this.reactionTime > 0) return;
        
        const distance = Math.abs(this.x - this.target.x);
        const isPlayerLeft = this.target.x < this.x;
        const isPlayerRight = this.target.x > this.x;
        
        switch (this.aiState) {
            case 'approach':
                this.handleApproach(keys, isPlayerLeft, isPlayerRight);
                break;
                
            case 'attack':
                this.handleAttack(keys, distance);
                break;
                
            case 'counter':
                this.handleCounterAttack(keys, distance);
                break;
                
            case 'defend':
                this.handleDefense(keys, isPlayerLeft, isPlayerRight);
                break;
                
            case 'close_combat':
                this.handleCloseCombat(keys, distance, isPlayerLeft, isPlayerRight);
                break;
                
            case 'tactical':
                this.handleTactical(keys, isPlayerLeft, isPlayerRight);
                break;
                
            case 'desperate':
                this.handleDesperate(keys, distance, isPlayerLeft, isPlayerRight);
                break;
        }
        
        // Advanced movement techniques
        this.handleAdvancedMovement(keys, distance);
    }

    handleApproach(keys, isPlayerLeft, isPlayerRight) {
        // Move towards predicted position instead of current position
        const targetX = this.predictedPlayerPos.x || this.target.x;
        
        if (targetX < this.x - 5) {
            keys[this.controls.left] = true;
            this.facingRight = false;
        } else if (targetX > this.x + 5) {
            keys[this.controls.right] = true;
            this.facingRight = true;
        }
        
        // Jump over obstacles or to reach player
        if (this.target.y < this.y - 20 && this.onGround && Math.random() < 0.3) {
            keys[this.controls.jump] = true;
        }
    }

    handleAttack(keys, distance) {
        // Face the target
        this.facingRight = this.target.x > this.x;
        
        // Feint attack sometimes
        if (Math.random() < this.feintChance && !this.isFeinting) {
            this.isFeinting = true;
            this.nextAction = 10; // Delay real attack
            return;
        }
        
        if (this.isFeinting && this.nextAction > 0) {
            this.nextAction--;
            return;
        }
        
        this.isFeinting = false;
        
        // Attack with high accuracy
        if (distance < this.attackRange + 15 && Math.random() < this.attackAccuracy) {
            keys[this.controls.attack] = true;
            this.consecutiveAttacks++;
            this.lastAttackTime = Date.now();
            
            // Set reaction time for next decision
            this.reactionTime = this.reactionSpeed;
        }
    }

    handleCounterAttack(keys, distance) {
        // Perfect counter timing
        this.facingRight = this.target.x > this.x;
        
        if (distance < this.attackRange + 20) {
            keys[this.controls.attack] = true;
            this.reactionTime = 1; // Very fast reaction
        }
    }

    handleDefense(keys, isPlayerLeft, isPlayerRight) {
        // Defensive movement - back away slightly
        if (isPlayerLeft) {
            keys[this.controls.right] = true;
        } else if (isPlayerRight) {
            keys[this.controls.left] = true;
        }
        
        // Jump to avoid low attacks
        if (this.target.isAttacking && this.onGround && Math.random() < 0.4) {
            keys[this.controls.jump] = true;
        }
    }

    handleCloseCombat(keys, distance, isPlayerLeft, isPlayerRight) {
        // Aggressive close combat
        if (distance > this.attackRange - 10) {
            // Get closer
            if (isPlayerLeft) {
                keys[this.controls.left] = true;
            } else if (isPlayerRight) {
                keys[this.controls.right] = true;
            }
        }
        
        // Quick attacks in close combat
        if (this.attackCooldown === 0 && Math.random() < 0.7) {
            keys[this.controls.attack] = true;
        }
    }

    handleTactical(keys, isPlayerLeft, isPlayerRight) {
        // Tactical movement - circle around player
        const shouldCircle = Math.random() < 0.6;
        
        if (shouldCircle) {
            // Circle movement
            if (this.facingRight) {
                keys[this.controls.right] = true;
            } else {
                keys[this.controls.left] = true;
            }
        } else {
            // Standard approach
            if (isPlayerLeft) {
                keys[this.controls.left] = true;
            } else if (isPlayerRight) {
                keys[this.controls.right] = true;
            }
        }
    }

    handleDesperate(keys, distance, isPlayerLeft, isPlayerRight) {
        // Ultra aggressive - always attack when possible
        this.facingRight = this.target.x > this.x;
        
        // Rush towards player
        if (isPlayerLeft) {
            keys[this.controls.left] = true;
        } else if (isPlayerRight) {
            keys[this.controls.right] = true;
        }
        
        // Attack constantly
        if (this.attackCooldown === 0) {
            keys[this.controls.attack] = true;
        }
        
        // Jump aggressively
        if (this.onGround && Math.random() < 0.5) {
            keys[this.controls.jump] = true;
        }
    }

    handleAdvancedMovement(keys, distance) {
        // Advanced AI movement techniques
        
        // Combo continuation
        if (this.consecutiveAttacks > 0 && this.consecutiveAttacks < 3 && 
            Date.now() - this.lastAttackTime < 800 && 
            Math.random() < this.comboChance) {
            if (distance < this.attackRange + 10 && this.attackCooldown === 0) {
                keys[this.controls.attack] = true;
            }
        }
        
        // Reset combo counter
        if (Date.now() - this.lastAttackTime > 1000) {
            this.consecutiveAttacks = 0;
        }
    }

    updateAITimers() {
        if (this.nextAction > 0) {
            this.nextAction--;
        }
    }

    // Override takeDamage to add AI reaction
    takeDamage(damage, attackerX) {
        super.takeDamage(damage, attackerX);
        
        // AI reacts to taking damage
        this.reactionTime = Math.max(1, this.reactionSpeed - 1);
        
        // Become more aggressive when hurt
        this.aggressiveness = Math.min(1.0, this.aggressiveness + 0.05);
        
        // Reset AI state to reassess situation
        this.aiState = 'defend';
    }
}
